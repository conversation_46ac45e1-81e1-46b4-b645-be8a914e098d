import os
import sys

# 导入Save类
sys.path.append('.')
from kayak import Save

def test_save_class():
    """测试Save类的功能"""
    print("=== 测试Save类功能 ===\n")
    
    # 测试1: 创建新的CSV文件
    print("1. 测试创建新CSV文件:")
    test_filename = 'test_hotels.csv'
    
    # 如果测试文件存在，先删除
    if os.path.exists(test_filename):
        os.remove(test_filename)
        print(f"已删除旧的测试文件: {test_filename}")
    
    # 创建Save实例
    saver = Save(test_filename)
    
    # 检查文件信息
    file_info = saver.get_file_info()
    print(f"文件信息: {file_info}")
    
    # 测试2: 写入单条数据
    print("\n2. 测试写入单条数据:")
    test_data1 = {
        'HotelId': 'p12345-h67890',
        'HotelName': '测试酒店1',
        'HotelAddress': '123 Test Street, Houston, TX',
        'LocationIP': '29.7604,-95.3698',
        'HotelMonthPrice': 'January 120$,February 130$,March 140$',
        'HotelDayPrice': 'Sun 125$'
    }
    
    saver.write_data(test_data1)
    
    # 测试3: 写入多条数据
    print("\n3. 测试批量写入数据:")
    test_data_list = [
        {
            'HotelId': 'p11111-h22222',
            'HotelName': '测试酒店2',
            'HotelAddress': '456 Another Street, Houston, TX',
            'LocationIP': '29.7256,-95.3905',
            'HotelMonthPrice': 'January 100$,February 110$,March 120$',
            'HotelDayPrice': 'Sun 105$'
        },
        {
            'HotelId': 'p33333-h44444',
            'HotelName': '测试酒店3',
            'HotelAddress': '789 Third Avenue, Houston, TX',
            'LocationIP': '29.7153,-95.3891',
            'HotelMonthPrice': 'January 150$,February 160$,March 170$',
            'HotelDayPrice': 'Sun 155$'
        }
    ]
    
    saver.write_multiple_data(test_data_list)
    
    # 测试4: 检查最终文件信息
    print("\n4. 最终文件信息:")
    final_info = saver.get_file_info()
    print(f"最终文件信息: {final_info}")
    
    # 测试5: 读取并显示文件内容
    print("\n5. 文件内容预览:")
    try:
        with open(test_filename, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:6]):  # 只显示前6行
                print(f"第{i+1}行: {line.strip()}")
    except Exception as e:
        print(f"读取文件时出错: {e}")
    
    print(f"\n测试完成！测试文件: {test_filename}")
    
    # 测试6: 测试已存在文件的情况
    print("\n6. 测试已存在文件的情况:")
    saver2 = Save(test_filename)  # 使用相同文件名
    print("使用已存在的文件，不会重新创建表头")
    
    # 再写入一条数据
    additional_data = {
        'HotelId': 'p55555-h66666',
        'HotelName': '测试酒店4',
        'HotelAddress': '999 Final Street, Houston, TX',
        'LocationIP': '29.7599,-95.3690',
        'HotelMonthPrice': 'January 200$,February 210$,March 220$',
        'HotelDayPrice': 'Sun 205$'
    }
    
    saver2.write_data(additional_data)
    
    final_info2 = saver2.get_file_info()
    print(f"添加数据后的文件信息: {final_info2}")

if __name__ == '__main__':
    test_save_class()
