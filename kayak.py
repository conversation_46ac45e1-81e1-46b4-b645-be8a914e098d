import requests
from lxml import etree
import csv
import re
import json

clas

class Spider:
    def __init__(self):
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://www.ca.kayak.com',
            'priority': 'u=1, i',
            'referer': 'https://www.ca.kayak.com/hotels/Texas,United-States-p57056/2025-09-10/2025-09-11/2adults;map?ucs=18uuclw&sort=rank_a',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
            'x-csrf': '_U9Rv5u44y$1BEMJaV0vq5ad0hAOjRJrIZ9uvVhK_YM-DCpYWcf9r$rHkl_aQxZdt_5iCiVf4miMmiPCH$2Hq2w',
            'x-requested-with': 'XMLHttpRequest',
        }
        self.cookies = {
            'Apache': 'fPT3gg-AAABmTKMAdE-c5-KGSxsQ',
            'cluster': '5',
            'p1.med.sid': 'R-5WpDxBHYQcggkKUSBV3ez-XqzNVMjddD9PE2JnJYKJ5zRXOzn9zrZ9_LYqdEo0V',
            'kayak': 'P5Rt0IAB42JW9F9ekEXf',
            'csid': 'd8f674e9-4220-4328-b15b-e31cf0a3c3dc',
            'kmkid': 'AuqpsG8TtvWy3LwkAu8kW68',
            'captcha': 'UD9qj4X9Xjw:S1MdqX4exz$Xo1jmuPqreIkDK4ADOPOQ7LO2CYKeb0o',
            '_gcl_au': '1.1.1084466476.1757489685',
            '_fbp': 'fb.1.1757489685000.0.13581967074718826',
            '_ga': 'GA1.1.1539793648.1757489686',
            'FPGSID': '1.1757489689.1757489689.G-PWCRSK2Y5Y.PVZ_XFl1Y6h1DNSC9mipjQ',
            '_yoid': '417d0e04-0839-4406-9452-24068ce06daa',
            '_yosid': '8d8610c2-0561-41da-a375-8bbf31d80b8b',
            '_tt_enable_cookie': '1',
            '_ttp': '01K4S8RYXBSQ2NHJ4DZXRJERWY_.tt.1',
            'kayak.mc': 'AXkQBcVQa9k55hoMpKm3DO1AoBFJGgRhr2HrWGw2NZVHfMbudXdn8fd6V9tqAIBG5hazeLRvqjU2ygsCNn09CHmqw6fO8Zx1b_68DThVLyLEk00RvD7gZHOaCHOFeeagQxFOEk9lpfmsXdpG_N-xppdDSYYldr2zR9Plf4sWiX5RfcKtCXOP6MD1txtMKQvXAYYScZ9gGW285z_S6-gpaGqtpHtSnSZlzNgqx8Or7NtrZTDnAGDWVTYJbDtMIFwzDAgw-TY3tn-OsrCkanT9shJt7FGMWTnCjb3ck08JVrd-SE0oxswjGLBtxCx3d1Pa5NkMnTzJGbSjqYz4D0dUq0s04uUCBBKdr3zin9RTglO34k5dEdCtvkP9y9XBoDaucRaBtZJ0COCj_Y9DHayno-3jK_fHukztinZYqB40JLO3',
            '_ga_PWCRSK2Y5Y': 'GS2.1.s1757489685$o1$g1$t1757489712$j33$l0$h992757332',
            '_uetsid': 'a2aaf0f08e1811f0afa3599cf16cc1d7|ou6vpy|2|fz7|0|2079',
            '_uetvid': 'a2aaf3108e1811f0ab2e2d959bc8f5fe|xnv70p|1757489713636|2|1|bat.bing.com/p/insights/c/d',
            'ttcsid': '1757489691565::MZPsH5kTgx3oeBAan0gH.1.1757489713822',
            'ttcsid_C9P4TJBC77U4F2PRRBV0': '1757489691564::OtG_XF-VeeT09zvC7ALa.1.1757489714081',
            'forterToken': 'fa447510b84e45be9f331ed23d0c83af_1757489711194__UDF43-m4_21ck_',
            'mst_iBfK2w': 'PglT159ECsf3VU2hi1twZFLVADMMyC8xrUyCu0Xxjqp1-t9gHTKlVIuqun3DuFvwlYJtAIP9ny7h2h1DFbkzBA',
            'mst_ADIrlA': 'O19Ag5dfj_IsMAla1wth51LVADMMyC8xrUyCu0Xxjqq5pKsvArTSCKBnlv1AEHhO9wYRf7SaOlPjz9PUmd2UcA',
        }
    def get_list_data(self,page=1,get_num=1): #页数和请求次数
        url = 'https://www.ca.kayak.com/i/api/search/dynamic/hotels/poll'
        json_data = {
            'filterParams': {},
            'sortParams': {
                'sortMode': 'rank_a',
            },
            'userSearchParams': {
                'searchLocation': {
                    'locationType': 'place',
                    'locationQuery': '57056',
                },
                'adults': '2',
                'checkin': '2025-09-10',
                'checkout': '2025-09-11',
                'rooms': '1',
                'childAges': [],
                'searchId': 'nHGEThetLK',
                'pageType': 'results',
            },
            'pageNumber': page,
            'metadata': {
                'impressionCount': get_num,
            },
        }
        response = requests.post(url, headers=self.headers,cookies=self.cookies, json=json_data)
        return response.json()
    def get_detail_data(self,url):
        url = 'https://www.ca.kayak.com/' + url
        params = {
            'psid': 'nHGEThetLK',
            'pm': 'nightly-base',
        }   
        response = requests.get(url, headers=self.headers,cookies=self.cookies, params=params)
        return response.text
    def get_price(self,id):
        params = {
            'locationId': id,
            'locationType': 'hotel',
            'propertyTypeIds': 'hotel',
        }
        response = requests.get('https://www.ca.kayak.com/i/api/seo/chart/v1/staysWhenToBookChart', headers=self.headers,cookies=self.cookies, params=params)
        return response.json()
    

def sishewuru(num):
    #四舍五入只保留整数
    return int(num + 0.5)
if __name__ == '__main__':
    spider = Spider()
    data = spider.get_list_data()
    for i in data['results']:
        id = i['resultId']
        data_csv = {}
        url = i['detailsUrl']
        data_csv['HotelId'] = re.search(r'p\d+-h\d+', url).group()
        data_csv['HotelName'] = i['localizedHotelName']
        detail_data = spider.get_detail_data(url)
        detail_data = etree.HTML(detail_data)

        data_csv['HotelAddress'] = detail_data.xpath('//div[@class="c3xth-address"]/text()')[0]

        script_json = detail_data.xpath('//script[@id="__R9_HYDRATE_DATA__"]/text()')[0]
        script_json = json.loads(script_json)
        location = script_json["serverData"]["contentState"]["locationData"]["mapState"]["home"]["coordinates"]
        data_csv['LocationIP'] = f'{location["lat"]},{location["lng"]}'

        month_list = ["January","February","March","April","May","June","July","August","September","October","November","December"]
        
        price_data = spider.get_price(id)
        data_csv['HotelMonthPrice'] = ''
        for index_num,month_price in enumerate(price_data["months"]["values"]):
            data_csv['HotelMonthPrice'] += f'{month_list[index_num]} {sishewuru(month_price)}$,'
        data_csv['HotelMonthPrice'] = data_csv['HotelMonthPrice'][:-1]
        
        data_csv['HotelDayPrice'] = f'Sun {sishewuru(price_data["days"]["values"][0])}$'
        print(data_csv)