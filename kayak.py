import requests
from lxml import etree
import csv
import re
import json
import os

class Save:
    def __init__(self, filename='hotels_data.csv'):
        """
        初始化Save类
        :param filename: CSV文件名，默认为'hotels_data.csv'
        """
        self.filename = filename
        self.fieldnames = [
            'HotelId', 'HotelName', 'HotelAddress',
            'LocationIP', 'HotelMonthPrice', 'HotelDayPrice'
        ]
        self.file_exists = os.path.exists(self.filename)
        self.existing_hotel_ids = set()  # 用于存储已存在的酒店ID

        # 如果文件不存在，创建文件并写入表头
        if not self.file_exists:
            self.create_csv_with_header()
        else:
            # 如果文件存在，读取已有的酒店ID用于去重
            self.load_existing_hotel_ids()

    def create_csv_with_header(self):
        """创建CSV文件并写入表头"""
        try:
            with open(self.filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
                writer.writeheader()
            print(f"已创建CSV文件: {self.filename}")
            self.file_exists = True
        except Exception as e:
            print(f"创建CSV文件时出错: {e}")

    def load_existing_hotel_ids(self):
        """从现有CSV文件中加载已存在的酒店ID"""
        try:
            with open(self.filename, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if 'HotelId' in row and row['HotelId']:
                        self.existing_hotel_ids.add(row['HotelId'])
            print(f"已加载 {len(self.existing_hotel_ids)} 个现有酒店ID用于去重")
        except Exception as e:
            print(f"加载现有酒店ID时出错: {e}")
            self.existing_hotel_ids = set()

    def is_duplicate(self, hotel_id):
        """检查酒店ID是否已存在"""
        return hotel_id in self.existing_hotel_ids

    def add_hotel_id(self, hotel_id):
        """添加酒店ID到已存在集合中"""
        self.existing_hotel_ids.add(hotel_id)

    def write_data(self, data_dict, check_duplicate=True):
        """
        写入单条数据到CSV文件
        :param data_dict: 包含酒店数据的字典
        :param check_duplicate: 是否检查重复，默认为True
        :return: True表示成功写入，False表示重复跳过
        """
        hotel_id = data_dict.get('HotelId', '')

        # 检查是否重复
        if check_duplicate and self.is_duplicate(hotel_id):
            print(f"⚠️  跳过重复数据: {data_dict.get('HotelName', '未知酒店')} (ID: {hotel_id})")
            return False

        try:
            with open(self.filename, 'a', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
                writer.writerow(data_dict)

            # 添加到已存在集合中
            if hotel_id:
                self.add_hotel_id(hotel_id)

            print(f"✓ 已写入数据: {data_dict.get('HotelName', '未知酒店')}")
            return True
        except Exception as e:
            print(f"写入数据时出错: {e}")
            return False

    def write_multiple_data(self, data_list, check_duplicate=True):
        """
        写入多条数据到CSV文件
        :param data_list: 包含多个酒店数据字典的列表
        :param check_duplicate: 是否检查重复，默认为True
        :return: 返回写入成功的数量和跳过的数量
        """
        written_count = 0
        skipped_count = 0

        try:
            with open(self.filename, 'a', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)

                for data_dict in data_list:
                    hotel_id = data_dict.get('HotelId', '')

                    # 检查是否重复
                    if check_duplicate and self.is_duplicate(hotel_id):
                        print(f"⚠️  跳过重复数据: {data_dict.get('HotelName', '未知酒店')} (ID: {hotel_id})")
                        skipped_count += 1
                        continue

                    writer.writerow(data_dict)

                    # 添加到已存在集合中
                    if hotel_id:
                        self.add_hotel_id(hotel_id)

                    written_count += 1

            print(f"批量写入完成: 成功 {written_count} 条，跳过重复 {skipped_count} 条")
            return written_count, skipped_count

        except Exception as e:
            print(f"批量写入数据时出错: {e}")
            return 0, 0

    def check_file_exists(self):
        """检查CSV文件是否存在"""
        return os.path.exists(self.filename)

    def get_file_info(self):
        """获取CSV文件信息"""
        if self.check_file_exists():
            file_size = os.path.getsize(self.filename)
            return {
                'filename': self.filename,
                'exists': True,
                'size': file_size,
                'size_mb': round(file_size / 1024 / 1024, 2)
            }
        else:
            return {
                'filename': self.filename,
                'exists': False,
                'size': 0,
                'size_mb': 0
            }
    
class Spider:
    def __init__(self):
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://www.ca.kayak.com',
            'priority': 'u=1, i',
            'referer': 'https://www.ca.kayak.com/hotels/Texas,United-States-p57056/2025-09-10/2025-09-11/2adults;map?ucs=18uuclw&sort=rank_a',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
            'x-csrf': '_U9Rv5u44y$1BEMJaV0vq5ad0hAOjRJrIZ9uvVhK_YM-DCpYWcf9r$rHkl_aQxZdt_5iCiVf4miMmiPCH$2Hq2w',
            'x-requested-with': 'XMLHttpRequest',
        }
        self.cookies = {
            'Apache': 'fPT3gg-AAABmTKMAdE-c5-KGSxsQ',
            'cluster': '5',
            'p1.med.sid': 'R-5WpDxBHYQcggkKUSBV3ez-XqzNVMjddD9PE2JnJYKJ5zRXOzn9zrZ9_LYqdEo0V',
            'kayak': 'P5Rt0IAB42JW9F9ekEXf',
            'csid': 'd8f674e9-4220-4328-b15b-e31cf0a3c3dc',
            'kmkid': 'AuqpsG8TtvWy3LwkAu8kW68',
            'captcha': 'UD9qj4X9Xjw:S1MdqX4exz$Xo1jmuPqreIkDK4ADOPOQ7LO2CYKeb0o',
            '_gcl_au': '1.1.1084466476.1757489685',
            '_fbp': 'fb.1.1757489685000.0.13581967074718826',
            '_ga': 'GA1.1.1539793648.1757489686',
            'FPGSID': '1.1757489689.1757489689.G-PWCRSK2Y5Y.PVZ_XFl1Y6h1DNSC9mipjQ',
            '_yoid': '417d0e04-0839-4406-9452-24068ce06daa',
            '_yosid': '8d8610c2-0561-41da-a375-8bbf31d80b8b',
            '_tt_enable_cookie': '1',
            '_ttp': '01K4S8RYXBSQ2NHJ4DZXRJERWY_.tt.1',
            'kayak.mc': 'AXkQBcVQa9k55hoMpKm3DO1AoBFJGgRhr2HrWGw2NZVHfMbudXdn8fd6V9tqAIBG5hazeLRvqjU2ygsCNn09CHmqw6fO8Zx1b_68DThVLyLEk00RvD7gZHOaCHOFeeagQxFOEk9lpfmsXdpG_N-xppdDSYYldr2zR9Plf4sWiX5RfcKtCXOP6MD1txtMKQvXAYYScZ9gGW285z_S6-gpaGqtpHtSnSZlzNgqx8Or7NtrZTDnAGDWVTYJbDtMIFwzDAgw-TY3tn-OsrCkanT9shJt7FGMWTnCjb3ck08JVrd-SE0oxswjGLBtxCx3d1Pa5NkMnTzJGbSjqYz4D0dUq0s04uUCBBKdr3zin9RTglO34k5dEdCtvkP9y9XBoDaucRaBtZJ0COCj_Y9DHayno-3jK_fHukztinZYqB40JLO3',
            '_ga_PWCRSK2Y5Y': 'GS2.1.s1757489685$o1$g1$t1757489712$j33$l0$h992757332',
            '_uetsid': 'a2aaf0f08e1811f0afa3599cf16cc1d7|ou6vpy|2|fz7|0|2079',
            '_uetvid': 'a2aaf3108e1811f0ab2e2d959bc8f5fe|xnv70p|1757489713636|2|1|bat.bing.com/p/insights/c/d',
            'ttcsid': '1757489691565::MZPsH5kTgx3oeBAan0gH.1.1757489713822',
            'ttcsid_C9P4TJBC77U4F2PRRBV0': '1757489691564::OtG_XF-VeeT09zvC7ALa.1.1757489714081',
            'forterToken': 'fa447510b84e45be9f331ed23d0c83af_1757489711194__UDF43-m4_21ck_',
            'mst_iBfK2w': 'PglT159ECsf3VU2hi1twZFLVADMMyC8xrUyCu0Xxjqp1-t9gHTKlVIuqun3DuFvwlYJtAIP9ny7h2h1DFbkzBA',
            'mst_ADIrlA': 'O19Ag5dfj_IsMAla1wth51LVADMMyC8xrUyCu0Xxjqq5pKsvArTSCKBnlv1AEHhO9wYRf7SaOlPjz9PUmd2UcA',
        }
    def get_list_data(self,page=1,get_num=1): #页数和请求次数
        url = 'https://www.ca.kayak.com/i/api/search/dynamic/hotels/poll'
        json_data = {
            'filterParams': {},
            'sortParams': {
                'sortMode': 'rank_a',
            },
            'userSearchParams': {
                'searchLocation': {
                    'locationType': 'place',
                    'locationQuery': '57056',
                },
                'adults': '2',
                'checkin': '2025-09-10',
                'checkout': '2025-09-11',
                'rooms': '1',
                'childAges': [],
                'searchId': 'nHGEThetLK',
                'pageType': 'results',
            },
            'pageNumber': page,
            'metadata': {
                'impressionCount': get_num,
            },
        }
        response = requests.post(url, headers=self.headers,cookies=self.cookies, json=json_data)
        return response.json()
    def get_detail_data(self,url):
        url = 'https://www.ca.kayak.com/' + url
        params = {
            'psid': 'nHGEThetLK',
            'pm': 'nightly-base',
        }   
        response = requests.get(url, headers=self.headers,cookies=self.cookies, params=params)
        return response.text
    def get_price(self,id):
        params = {
            'locationId': id,
            'locationType': 'hotel',
            'propertyTypeIds': 'hotel',
        }
        response = requests.get('https://www.ca.kayak.com/i/api/seo/chart/v1/staysWhenToBookChart', headers=self.headers,cookies=self.cookies, params=params)
        return response.json()

    def get_multiple_pages(self, start_page=1, max_pages=5, get_num=1):
        """
        获取多页数据
        :param start_page: 起始页码，默认为1
        :param max_pages: 最大页数，默认为5
        :param get_num: 每页请求次数，默认为1
        :return: 所有页面的酒店数据列表
        """
        all_hotels = []

        for page in range(start_page, start_page + max_pages):
            print(f"\n正在获取第 {page} 页数据...")

            try:
                data = self.get_list_data(page=page, get_num=get_num)

                if 'results' in data and data['results']:
                    hotels_count = len(data['results'])
                    print(f"第 {page} 页找到 {hotels_count} 个酒店")
                    all_hotels.extend(data['results'])
                else:
                    print(f"第 {page} 页没有找到酒店数据，可能已到最后一页")
                    break

            except Exception as e:
                print(f"获取第 {page} 页数据时出错: {e}")
                continue

        print(f"\n总共获取到 {len(all_hotels)} 个酒店数据")
        return all_hotels
    

def sishewuru(num):
    #四舍五入只保留整数
    return int(num + 0.5)

if __name__ == '__main__':
    # 初始化爬虫和保存类
    spider = Spider()
    saver = Save('kayak_hotels.csv')  # 指定CSV文件名

    # 显示文件信息
    file_info = saver.get_file_info()
    print(f"CSV文件信息: {file_info}")

    # 配置爬取参数
    START_PAGE = 1      # 起始页码
    MAX_PAGES = 3       # 最大爬取页数
    GET_NUM = 1         # 每页请求次数

    print(f"开始爬取数据: 从第{START_PAGE}页开始，最多爬取{MAX_PAGES}页")

    # 获取多页酒店列表数据
    all_hotels = spider.get_multiple_pages(
        start_page=START_PAGE,
        max_pages=MAX_PAGES,
        get_num=GET_NUM
    )

    if not all_hotels:
        print("没有获取到任何酒店数据，程序结束")
        exit()

    # 统计信息
    total_hotels = len(all_hotels)
    processed_count = 0
    success_count = 0
    duplicate_count = 0
    error_count = 0

    print(f"\n开始处理 {total_hotels} 个酒店的详细数据...")

    # 处理每个酒店的数据
    for index, i in enumerate(all_hotels, 1):
        try:
            print(f"\n正在处理第 {index}/{total_hotels} 个酒店...")
            processed_count += 1

            id = i['resultId']
            data_csv = {}
            url = i['detailsUrl']

            # 基本信息
            hotel_id = re.search(r'p\d+-h\d+', url).group()
            data_csv['HotelId'] = hotel_id
            data_csv['HotelName'] = i['localizedHotelName']

            # 检查是否重复（提前检查，避免不必要的网络请求）
            if saver.is_duplicate(hotel_id):
                print(f"⚠️  跳过重复酒店: {data_csv['HotelName']} (ID: {hotel_id})")
                duplicate_count += 1
                continue

            # 获取详情页数据
            detail_data = spider.get_detail_data(url)
            detail_data = etree.HTML(detail_data)

            # 地址信息
            address_elements = detail_data.xpath('//div[@class="c3xth-address"]/text()')
            data_csv['HotelAddress'] = address_elements[0] if address_elements else '地址未找到'

            # 经纬度信息
            try:
                script_json = detail_data.xpath('//script[@id="__R9_HYDRATE_DATA__"]/text()')[0]
                script_json = json.loads(script_json)
                location = script_json["serverData"]["contentState"]["locationData"]["mapState"]["home"]["coordinates"]
                data_csv['LocationIP'] = f'{location["lat"]},{location["lng"]}'
            except Exception as e:
                print(f"获取经纬度失败: {e}")
                data_csv['LocationIP'] = '经纬度未找到'

            # 价格信息
            month_list = ["January","February","March","April","May","June","July","August","September","October","November","December"]

            try:
                price_data = spider.get_price(id)
                data_csv['HotelMonthPrice'] = ''
                for index_num, month_price in enumerate(price_data["months"]["values"]):
                    data_csv['HotelMonthPrice'] += f'{month_list[index_num]} {sishewuru(month_price)}$,'
                data_csv['HotelMonthPrice'] = data_csv['HotelMonthPrice'][:-1]

                data_csv['HotelDayPrice'] = f'Sun {sishewuru(price_data["days"]["values"][0])}$'
            except Exception as e:
                print(f"获取价格信息失败: {e}")
                data_csv['HotelMonthPrice'] = '价格信息未找到'
                data_csv['HotelDayPrice'] = '价格信息未找到'

            # 保存数据到CSV（已经检查过重复，这里不再检查）
            if saver.write_data(data_csv, check_duplicate=False):
                success_count += 1

        except Exception as e:
            print(f"✗ 处理第 {index} 个酒店时出错: {e}")
            error_count += 1
            continue

    # 显示最终统计信息
    print(f"\n" + "="*50)
    print(f"数据爬取完成！统计信息:")
    print(f"总酒店数量: {total_hotels}")
    print(f"已处理数量: {processed_count}")
    print(f"成功保存: {success_count}")
    print(f"重复跳过: {duplicate_count}")
    print(f"处理失败: {error_count}")
    print(f"数据文件: {saver.filename}")

    # 显示最终文件信息
    final_file_info = saver.get_file_info()
    print(f"文件大小: {final_file_info['size_mb']} MB")
    print("="*50)