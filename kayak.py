import requests
from lxml import etree
import csv
import re
import json
import os

class Save:
    def __init__(self, filename='hotels_data.csv'):
        self.filename = filename
        self.fieldnames = ['HotelId', 'HotelName', 'HotelAddress', 'LocationIP', 'HotelMonthPrice', 'HotelDayPrice']
        self.existing_ids = set()

        # 如果文件不存在，创建并写入表头
        if not os.path.exists(self.filename):
            with open(self.filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=self.fieldnames)
                writer.writeheader()
            print(f"创建新文件: {self.filename}")
        else:
            # 读取已存在的ID
            with open(self.filename, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['HotelId']:
                        self.existing_ids.add(row['HotelId'])
            print(f"加载已有数据: {len(self.existing_ids)} 个酒店ID")

    def is_duplicate(self, hotel_id):
        return hotel_id in self.existing_ids

    def write_data(self, data_dict):
        with open(self.filename, 'a', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=self.fieldnames)
            writer.writerow(data_dict)
        self.existing_ids.add(data_dict['HotelId'])
        print(f"保存: {data_dict['HotelName']}")
    
class Spider:
    def __init__(self):
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://www.ca.kayak.com',
            'priority': 'u=1, i',
            'referer': 'https://www.ca.kayak.com/hotels/Texas,United-States-p57056/2025-09-10/2025-09-11/2adults;map?ucs=18uuclw&sort=rank_a',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
            'x-csrf': '_U9Rv5u44y$1BEMJaV0vq5ad0hAOjRJrIZ9uvVhK_YM-DCpYWcf9r$rHkl_aQxZdt_5iCiVf4miMmiPCH$2Hq2w',
            'x-requested-with': 'XMLHttpRequest',
        }
        self.cookies = {
            'Apache': 'fPT3gg-AAABmTKMAdE-c5-KGSxsQ',
            'cluster': '5',
            'p1.med.sid': 'R-5WpDxBHYQcggkKUSBV3ez-XqzNVMjddD9PE2JnJYKJ5zRXOzn9zrZ9_LYqdEo0V',
            'kayak': 'P5Rt0IAB42JW9F9ekEXf',
            'csid': 'd8f674e9-4220-4328-b15b-e31cf0a3c3dc',
            'kmkid': 'AuqpsG8TtvWy3LwkAu8kW68',
            'captcha': 'UD9qj4X9Xjw:S1MdqX4exz$Xo1jmuPqreIkDK4ADOPOQ7LO2CYKeb0o',
            '_gcl_au': '1.1.1084466476.1757489685',
            '_fbp': 'fb.1.1757489685000.0.13581967074718826',
            '_ga': 'GA1.1.1539793648.1757489686',
            'FPGSID': '1.1757489689.1757489689.G-PWCRSK2Y5Y.PVZ_XFl1Y6h1DNSC9mipjQ',
            '_yoid': '417d0e04-0839-4406-9452-24068ce06daa',
            '_yosid': '8d8610c2-0561-41da-a375-8bbf31d80b8b',
            '_tt_enable_cookie': '1',
            '_ttp': '01K4S8RYXBSQ2NHJ4DZXRJERWY_.tt.1',
            'kayak.mc': 'AXkQBcVQa9k55hoMpKm3DO1AoBFJGgRhr2HrWGw2NZVHfMbudXdn8fd6V9tqAIBG5hazeLRvqjU2ygsCNn09CHmqw6fO8Zx1b_68DThVLyLEk00RvD7gZHOaCHOFeeagQxFOEk9lpfmsXdpG_N-xppdDSYYldr2zR9Plf4sWiX5RfcKtCXOP6MD1txtMKQvXAYYScZ9gGW285z_S6-gpaGqtpHtSnSZlzNgqx8Or7NtrZTDnAGDWVTYJbDtMIFwzDAgw-TY3tn-OsrCkanT9shJt7FGMWTnCjb3ck08JVrd-SE0oxswjGLBtxCx3d1Pa5NkMnTzJGbSjqYz4D0dUq0s04uUCBBKdr3zin9RTglO34k5dEdCtvkP9y9XBoDaucRaBtZJ0COCj_Y9DHayno-3jK_fHukztinZYqB40JLO3',
            '_ga_PWCRSK2Y5Y': 'GS2.1.s1757489685$o1$g1$t1757489712$j33$l0$h992757332',
            '_uetsid': 'a2aaf0f08e1811f0afa3599cf16cc1d7|ou6vpy|2|fz7|0|2079',
            '_uetvid': 'a2aaf3108e1811f0ab2e2d959bc8f5fe|xnv70p|1757489713636|2|1|bat.bing.com/p/insights/c/d',
            'ttcsid': '1757489691565::MZPsH5kTgx3oeBAan0gH.1.1757489713822',
            'ttcsid_C9P4TJBC77U4F2PRRBV0': '1757489691564::OtG_XF-VeeT09zvC7ALa.1.1757489714081',
            'forterToken': 'fa447510b84e45be9f331ed23d0c83af_1757489711194__UDF43-m4_21ck_',
            'mst_iBfK2w': 'PglT159ECsf3VU2hi1twZFLVADMMyC8xrUyCu0Xxjqp1-t9gHTKlVIuqun3DuFvwlYJtAIP9ny7h2h1DFbkzBA',
            'mst_ADIrlA': 'O19Ag5dfj_IsMAla1wth51LVADMMyC8xrUyCu0Xxjqq5pKsvArTSCKBnlv1AEHhO9wYRf7SaOlPjz9PUmd2UcA',
        }
    def get_list_data(self,page=1,get_num=1): #页数和请求次数
        url = 'https://www.ca.kayak.com/i/api/search/dynamic/hotels/poll'
        json_data = {
            'filterParams': {},
            'sortParams': {
                'sortMode': 'rank_a',
            },
            'userSearchParams': {
                'searchLocation': {
                    'locationType': 'place',
                    'locationQuery': '57056',
                },
                'adults': '2',
                'checkin': '2025-09-10',
                'checkout': '2025-09-11',
                'rooms': '1',
                'childAges': [],
                'searchId': 'nHGEThetLK',
                'pageType': 'results',
            },
            'pageNumber': page,
            'metadata': {
                'impressionCount': get_num,
            },
        }
        response = requests.post(url, headers=self.headers,cookies=self.cookies, json=json_data)
        return response.json()
    def get_detail_data(self,url):
        url = 'https://www.ca.kayak.com/' + url
        params = {
            'psid': 'nHGEThetLK',
            'pm': 'nightly-base',
        }   
        response = requests.get(url, headers=self.headers,cookies=self.cookies, params=params)
        return response.text
    def get_price(self,id):
        params = {
            'locationId': id,
            'locationType': 'hotel',
            'propertyTypeIds': 'hotel',
        }
        response = requests.get('https://www.ca.kayak.com/i/api/seo/chart/v1/staysWhenToBookChart', headers=self.headers,cookies=self.cookies, params=params)
        return response.json()

        all_hotels = []
        for page in range(1, max_pages + 1):
            print(f"获取第 {page} 页...")
            try:
                data = self.get_list_data(page=page)
                if 'results' in data and data['results']:
                    all_hotels.extend(data['results'])
                    print(f"第 {page} 页: {len(data['results'])} 个酒店")
                else:
                    print(f"第 {page} 页无数据，停止")
                    break
            except Exception as e:
                print(f"第 {page} 页出错: {e}")
        print(f"总共获取: {len(all_hotels)} 个酒店")
        return all_hotels
    

def sishewuru(num):
    #四舍五入只保留整数
    return int(num + 0.5)

if __name__ == '__main__':
    spider = Spider()
    saver = Save('kayak_hotels.csv')


    for index, hotel in enumerate(all_hotels, 1):
        try:
            print(f"\n处理 {index}/{len(all_hotels)}")

            # 获取基本信息
            url = hotel['detailsUrl']
            hotel_id = re.search(r'p\d+-h\d+', url).group()

            # 检查重复
            if saver.is_duplicate(hotel_id):
                print(f"跳过重复: {hotel['localizedHotelName']}")
                duplicate += 1
                continue

            # 构建数据
            data = {
                'HotelId': hotel_id,
                'HotelName': hotel['localizedHotelName'],
                'HotelAddress': '',
                'LocationIP': '',
                'HotelMonthPrice': '',
                'HotelDayPrice': ''
            }

            # 获取详情页
            detail_html = spider.get_detail_data(url)
            detail_tree = etree.HTML(detail_html)

            # 地址
            address = detail_tree.xpath('//div[@class="c3xth-address"]/text()')
            data['HotelAddress'] = address[0] if address else '未找到'

            # 经纬度
            try:
                script = detail_tree.xpath('//script[@id="__R9_HYDRATE_DATA__"]/text()')[0]
                json_data = json.loads(script)
                coords = json_data["serverData"]["contentState"]["locationData"]["mapState"]["home"]["coordinates"]
                data['LocationIP'] = f'{coords["lat"]},{coords["lng"]}'
            except:
                data['LocationIP'] = '未找到'

            # 价格
            try:
                price_data = spider.get_price(hotel['resultId'])
                months = ["January","February","March","April","May","June","July","August","September","October","November","December"]
                month_prices = []
                for i, price in enumerate(price_data["months"]["values"]):
                    month_prices.append(f'{months[i]} {sishewuru(price)}$')
                data['HotelMonthPrice'] = ','.join(month_prices)
                data['HotelDayPrice'] = f'Sun {sishewuru(price_data["days"]["values"][0])}$'
            except:
                data['HotelMonthPrice'] = '未找到'
                data['HotelDayPrice'] = '未找到'

            # 保存
            saver.write_data(data)
            success += 1

        except Exception as e:
            print(f"处理失败: {e}")

    print(f"\n完成!")